<?php
/**
 * Admin Interface for CrawlGuard WP
 */

if (!defined('ABSPATH')) {
    exit;
}

class CrawlGuard_Admin {

    private $jwt_validator;
    private $user_data = null;

    public function __construct() {
        // Initialize JWT validator
        $this->jwt_validator = new CrawlGuard_JWT_Validator();

        // Hook into WordPress admin
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_init', array($this, 'init_settings'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
        add_action('wp_ajax_crawlguard_get_analytics', array($this, 'ajax_get_analytics'));

        // Add admin notices for debugging
        add_action('admin_notices', array($this, 'admin_notices'));

        // Validate JWT token on admin init
        add_action('admin_init', array($this, 'validate_jwt_token'));

        // Add AJAX handler for JWT testing
        add_action('wp_ajax_crawlguard_test_jwt', array($this, 'ajax_test_jwt'));
        add_action('wp_ajax_crawlguard_generate_sample_jwt', array($this, 'ajax_generate_sample_jwt'));
    }
    
    public function add_admin_menu() {
        add_menu_page(
            'CrawlGuard WP',
            'CrawlGuard',
            'manage_options',
            'crawlguard',
            array($this, 'admin_page'),
            'dashicons-shield-alt',
            30
        );
        
        add_submenu_page(
            'crawlguard',
            'Dashboard',
            'Dashboard',
            'manage_options',
            'crawlguard',
            array($this, 'admin_page')
        );
        
        add_submenu_page(
            'crawlguard',
            'Settings',
            'Settings',
            'manage_options',
            'crawlguard-settings',
            array($this, 'settings_page')
        );
    }

    /**
     * Validate JWT token and extract user data
     */
    public function validate_jwt_token() {
        $options = get_option('crawlguard_options', array());
        $api_key = $options['api_key'] ?? '';

        if (empty($api_key)) {
            $this->user_data = null;
            return;
        }

        // Check if it's a JWT token (starts with 'eyJ')
        if (strpos($api_key, 'eyJ') === 0) {
            $validation_result = $this->jwt_validator->validate_token($api_key);

            if ($validation_result['valid']) {
                $this->user_data = $validation_result['payload'];
                // Cache the validation result for 1 hour
                set_transient('crawlguard_jwt_validation', $validation_result, 3600);
            } else {
                $this->user_data = null;
                // Store validation error for display
                set_transient('crawlguard_jwt_error', $validation_result['error'], 300);
            }
        } else {
            // Legacy API key format - maintain backward compatibility
            $this->user_data = array(
                'legacy_key' => true,
                'api_key' => $api_key
            );
        }
    }

    /**
     * Get user data from validated JWT
     */
    public function get_user_data() {
        return $this->user_data;
    }

    public function admin_page() {
        $options = get_option('crawlguard_options', array());
        $api_key = $options['api_key'] ?? '';
        $is_configured = !empty($api_key);
        $user_data = $this->get_user_data();
        $jwt_error = get_transient('crawlguard_jwt_error');
        ?>
        <div class="wrap">
            <h1>CrawlGuard WP Dashboard</h1>

            <?php if ($jwt_error): ?>
                <div class="notice notice-error">
                    <p><strong>JWT Validation Error:</strong> <?php echo esc_html($jwt_error); ?></p>
                    <p>Please check your API key in the <a href="<?php echo admin_url('admin.php?page=crawlguard-settings'); ?>">Settings</a> page.</p>
                </div>
            <?php elseif (!$is_configured): ?>
                <div class="notice notice-warning">
                    <p><strong>CrawlGuard Setup Required:</strong> Please configure your API key in the <a href="<?php echo admin_url('admin.php?page=crawlguard-settings'); ?>">Settings</a> page to start protecting your site.</p>
                </div>
            <?php elseif ($user_data && !isset($user_data['legacy_key'])): ?>
                <div class="notice notice-success">
                    <p><strong>CrawlGuard Active:</strong> JWT token validated successfully!</p>
                </div>
            <?php else: ?>
                <div class="notice notice-success">
                    <p><strong>CrawlGuard Active:</strong> Your site is protected with API key: <code><?php echo esc_html(substr($api_key, 0, 20) . '...'); ?></code></p>
                </div>
            <?php endif; ?>

            <div id="crawlguard-dashboard">
                <?php if ($user_data && !isset($user_data['legacy_key'])): ?>
                <div class="card">
                    <h2>Account Information</h2>
                    <table class="form-table">
                        <tr>
                            <th scope="row">User Email</th>
                            <td><?php echo esc_html($user_data['email'] ?? 'N/A'); ?></td>
                        </tr>
                        <tr>
                            <th scope="row">Subscription Plan</th>
                            <td><?php echo esc_html($user_data['subscription_plan'] ?? 'N/A'); ?></td>
                        </tr>
                        <tr>
                            <th scope="row">Subscription Status</th>
                            <td>
                                <span class="<?php echo ($user_data['subscription_status'] ?? '') === 'active' ? 'status-active' : 'status-inactive'; ?>">
                                    <?php echo esc_html(ucfirst($user_data['subscription_status'] ?? 'Unknown')); ?>
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">Site Limit</th>
                            <td><?php echo esc_html($user_data['site_limit'] ?? 'Unlimited'); ?></td>
                        </tr>
                        <tr>
                            <th scope="row">Sites Used</th>
                            <td><?php echo esc_html($user_data['sites_used'] ?? '0'); ?></td>
                        </tr>
                        <?php if (isset($user_data['expires_at'])): ?>
                        <tr>
                            <th scope="row">Expires</th>
                            <td><?php echo esc_html(date('F j, Y', $user_data['expires_at'])); ?></td>
                        </tr>
                        <?php endif; ?>
                    </table>
                </div>
                <?php endif; ?>

                <div class="card">
                    <h2>Bot Protection Status</h2>
                    <p><?php echo $is_configured ? 'Active and monitoring bot traffic' : 'Not configured - please add your API key'; ?></p>
                </div>

                <div class="card">
                    <h2>Quick Setup</h2>
                    <ol>
                        <li>Get your JWT API key from the AI Crawler Guard dashboard</li>
                        <li>Go to <a href="<?php echo admin_url('admin.php?page=crawlguard-settings'); ?>">CrawlGuard Settings</a></li>
                        <li>Paste your JWT token and save</li>
                        <li>Enable monetization if desired</li>
                    </ol>
                </div>
            </div>

            <style>
                .status-active { color: #46b450; font-weight: bold; }
                .status-inactive { color: #dc3232; font-weight: bold; }
                .form-table th { width: 150px; }
            </style>
        </div>
        <?php
    }
    
    public function settings_page() {
        $options = get_option('crawlguard_options', array());
        $api_key = $options['api_key'] ?? '';
        $user_data = $this->get_user_data();
        $jwt_error = get_transient('crawlguard_jwt_error');
        $is_jwt = !empty($api_key) && strpos($api_key, 'eyJ') === 0;
        ?>
        <div class="wrap">
            <h1>CrawlGuard Settings</h1>

            <?php settings_errors('crawlguard_options'); ?>

            <div class="card">
                <h2>API Configuration</h2>
                <p>Configure your CrawlGuard JWT token to start protecting your WordPress site from AI bots.</p>

                <form method="post" action="options.php">
                    <?php
                    settings_fields('crawlguard_settings');
                    do_settings_sections('crawlguard_settings');
                    submit_button('Save Settings');
                    ?>
                </form>
            </div>

            <?php if ($jwt_error): ?>
            <div class="card">
                <h2>JWT Validation Error</h2>
                <p><span class="dashicons dashicons-warning" style="color: red;"></span> <strong>Token Validation Failed</strong></p>
                <p><strong>Error:</strong> <?php echo esc_html($jwt_error); ?></p>
                <p>Please check your JWT token and try again.</p>
            </div>
            <?php elseif (!empty($api_key) && $is_jwt && $user_data): ?>
            <div class="card">
                <h2>JWT Token Status</h2>
                <p><span class="dashicons dashicons-yes-alt" style="color: green;"></span> <strong>JWT Token Valid</strong></p>
                <p><strong>Token Type:</strong> JWT (JSON Web Token)</p>
                <p><strong>User:</strong> <?php echo esc_html($user_data['email'] ?? 'N/A'); ?></p>
                <p><strong>Plan:</strong> <?php echo esc_html($user_data['subscription_plan'] ?? 'N/A'); ?></p>
                <p><strong>Status:</strong>
                    <span class="<?php echo ($user_data['subscription_status'] ?? '') === 'active' ? 'status-active' : 'status-inactive'; ?>">
                        <?php echo esc_html(ucfirst($user_data['subscription_status'] ?? 'Unknown')); ?>
                    </span>
                </p>
                <?php if (isset($user_data['expires_at'])): ?>
                <p><strong>Expires:</strong> <?php echo esc_html(date('F j, Y g:i A', $user_data['expires_at'])); ?></p>
                <?php endif; ?>
            </div>
            <?php elseif (!empty($api_key) && !$is_jwt): ?>
            <div class="card">
                <h2>Legacy API Key Status</h2>
                <p><span class="dashicons dashicons-info" style="color: orange;"></span> <strong>Legacy API Key Detected</strong></p>
                <p><strong>Current API Key:</strong> <code><?php echo esc_html(substr($api_key, 0, 20) . '...'); ?></code></p>
                <p><em>Consider upgrading to JWT tokens for enhanced security and features.</em></p>
            </div>
            <?php endif; ?>

            <div class="card">
                <h2>How to Get Your JWT Token</h2>
                <ol>
                    <li>Log in to your AI Crawler Guard dashboard</li>
                    <li>Go to the WordPress Sites section</li>
                    <li>Find your site and copy the JWT token</li>
                    <li>Paste it in the API Key field above</li>
                </ol>
                <p><strong>JWT Token format:</strong> Starts with <code>eyJ...</code> (much longer than legacy keys)</p>
                <p><strong>Legacy API Key format:</strong> <code>cg_e1b90f892dd106b9feaf5bc98d0457c7</code></p>
            </div>

            <div class="card">
                <h2>JWT Token Tester</h2>
                <p>Test any JWT token to see its contents and validation status.</p>
                <textarea id="jwt-test-input" placeholder="Paste JWT token here..." style="width: 100%; height: 100px; font-family: monospace; font-size: 12px;"></textarea>
                <p>
                    <button type="button" id="test-jwt-btn" class="button button-secondary">Test JWT Token</button>
                    <button type="button" id="generate-sample-jwt-btn" class="button button-secondary">Generate Sample JWT</button>
                </p>
                <div id="jwt-test-result" style="margin-top: 15px;"></div>
            </div>

            <style>
                .status-active { color: #46b450; font-weight: bold; }
                .status-inactive { color: #dc3232; font-weight: bold; }
                .notice.notice-success { border-left-color: #46b450; }
                .notice.notice-error { border-left-color: #dc3232; }
                .notice.notice-warning { border-left-color: #ffb900; }
                .jwt-result-success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 10px; border-radius: 4px; }
                .jwt-result-error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 10px; border-radius: 4px; }
                .jwt-payload { background: #f8f9fa; border: 1px solid #dee2e6; padding: 10px; border-radius: 4px; font-family: monospace; font-size: 12px; white-space: pre-wrap; }
            </style>

            <script>
            jQuery(document).ready(function($) {
                // Generate sample JWT token
                $('#generate-sample-jwt-btn').on('click', function() {
                    $(this).prop('disabled', true).text('Generating...');

                    $.ajax({
                        url: ajaxurl,
                        type: 'POST',
                        data: {
                            action: 'crawlguard_generate_sample_jwt',
                            nonce: '<?php echo wp_create_nonce('crawlguard_nonce'); ?>'
                        },
                        success: function(response) {
                            if (response.success) {
                                $('#jwt-test-input').val(response.data.token);
                            } else {
                                alert('Failed to generate sample token: ' + response.data);
                            }
                        },
                        error: function() {
                            alert('Failed to generate sample token. Please try again.');
                        },
                        complete: function() {
                            $('#generate-sample-jwt-btn').prop('disabled', false).text('Generate Sample JWT');
                        }
                    });
                });

                // Test JWT token
                $('#test-jwt-btn').on('click', function() {
                    var jwtToken = $('#jwt-test-input').val().trim();
                    var resultDiv = $('#jwt-test-result');

                    if (!jwtToken) {
                        resultDiv.html('<div class="jwt-result-error">Please enter a JWT token to test.</div>');
                        return;
                    }

                    $(this).prop('disabled', true).text('Testing...');

                    $.ajax({
                        url: ajaxurl,
                        type: 'POST',
                        data: {
                            action: 'crawlguard_test_jwt',
                            jwt_token: jwtToken,
                            nonce: '<?php echo wp_create_nonce('crawlguard_nonce'); ?>'
                        },
                        success: function(response) {
                            if (response.success) {
                                var html = '<div class="jwt-result-success">';
                                html += '<h4>✓ JWT Token Valid</h4>';
                                html += '<p><strong>User:</strong> ' + (response.data.user_info.email || 'N/A') + '</p>';
                                html += '<p><strong>Plan:</strong> ' + (response.data.user_info.subscription_plan || 'N/A') + '</p>';
                                html += '<p><strong>Status:</strong> ' + (response.data.user_info.subscription_status || 'N/A') + '</p>';
                                if (response.data.user_info.expires_at) {
                                    html += '<p><strong>Expires:</strong> ' + new Date(response.data.user_info.expires_at * 1000).toLocaleString() + '</p>';
                                }
                                html += '<h4>Full Payload:</h4>';
                                html += '<div class="jwt-payload">' + JSON.stringify(response.data.payload, null, 2) + '</div>';
                                html += '</div>';
                                resultDiv.html(html);
                            } else {
                                resultDiv.html('<div class="jwt-result-error"><h4>✗ JWT Validation Failed</h4><p>' + response.data + '</p></div>');
                            }
                        },
                        error: function() {
                            resultDiv.html('<div class="jwt-result-error"><h4>✗ Request Failed</h4><p>Could not test JWT token. Please try again.</p></div>');
                        },
                        complete: function() {
                            $('#test-jwt-btn').prop('disabled', false).text('Test JWT Token');
                        }
                    });
                });
            });
            </script>
        </div>
        <?php
    }
    
    public function init_settings() {
        register_setting('crawlguard_settings', 'crawlguard_options', array(
            'sanitize_callback' => array($this, 'sanitize_settings')
        ));

        add_settings_section(
            'crawlguard_main_section',
            'Main Settings',
            array($this, 'main_section_callback'),
            'crawlguard_settings'
        );

        add_settings_field(
            'api_key',
            'JWT Token / API Key',
            array($this, 'api_key_callback'),
            'crawlguard_settings',
            'crawlguard_main_section'
        );

        add_settings_field(
            'monetization_enabled',
            'Enable Monetization',
            array($this, 'monetization_enabled_callback'),
            'crawlguard_settings',
            'crawlguard_main_section'
        );
    }

    /**
     * Sanitize and validate settings
     */
    public function sanitize_settings($input) {
        $sanitized = array();

        // Sanitize API key/JWT token
        if (isset($input['api_key'])) {
            $api_key = trim($input['api_key']);

            // If it's a JWT token, validate it
            if (!empty($api_key) && strpos($api_key, 'eyJ') === 0) {
                $validation_result = $this->jwt_validator->validate_token($api_key);

                if (!$validation_result['valid']) {
                    add_settings_error(
                        'crawlguard_options',
                        'jwt_validation_error',
                        'JWT Token validation failed: ' . $validation_result['error'],
                        'error'
                    );
                    // Keep the old value if validation fails
                    $old_options = get_option('crawlguard_options', array());
                    $sanitized['api_key'] = $old_options['api_key'] ?? '';
                } else {
                    $sanitized['api_key'] = $api_key;
                    add_settings_error(
                        'crawlguard_options',
                        'jwt_validation_success',
                        'JWT Token validated successfully! User: ' . ($validation_result['payload']['email'] ?? 'Unknown'),
                        'success'
                    );
                    // Clear any previous JWT errors
                    delete_transient('crawlguard_jwt_error');
                }
            } else {
                // Legacy API key or empty
                $sanitized['api_key'] = sanitize_text_field($api_key);
            }
        }

        // Sanitize monetization setting
        $sanitized['monetization_enabled'] = isset($input['monetization_enabled']) ? (bool) $input['monetization_enabled'] : false;

        // Preserve other existing options
        $existing_options = get_option('crawlguard_options', array());
        $sanitized = array_merge($existing_options, $sanitized);

        return $sanitized;
    }
    
    public function main_section_callback() {
        echo '<p>Enter your JWT token or legacy API key from the AI Crawler Guard dashboard to connect your WordPress site.</p>';
    }

    public function api_key_callback() {
        $options = get_option('crawlguard_options', array());
        $api_key = $options['api_key'] ?? '';
        $is_jwt = !empty($api_key) && strpos($api_key, 'eyJ') === 0;
        ?>
        <textarea
               name="crawlguard_options[api_key]"
               class="regular-text"
               placeholder="eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9... (JWT Token) or cg_e1b90f892dd106b9feaf5bc98d0457c7 (Legacy)"
               style="width: 600px; height: 80px; font-family: monospace; font-size: 12px;"><?php echo esc_textarea($api_key); ?></textarea>
        <p class="description">
            <strong>JWT Token (Recommended):</strong> Starts with "eyJ" and contains encoded user data for enhanced security.<br>
            <strong>Legacy API Key:</strong> Starts with "cg_" followed by 32 characters (still supported).<br>
            <?php if ($is_jwt): ?>
                <span style="color: green;">✓ JWT Token detected</span>
            <?php elseif (!empty($api_key)): ?>
                <span style="color: orange;">⚠ Legacy API Key detected</span>
            <?php endif; ?>
        </p>
        <?php
    }
    
    public function monetization_enabled_callback() {
        $options = get_option('crawlguard_options');
        $enabled = $options['monetization_enabled'] ?? false;
        echo '<input type="checkbox" name="crawlguard_options[monetization_enabled]" value="1" ' . checked(1, $enabled, false) . ' />';
    }
    
    public function enqueue_admin_scripts($hook) {
        if (strpos($hook, 'crawlguard') === false) {
            return;
        }
        
        wp_enqueue_script(
            'crawlguard-admin',
            CRAWLGUARD_PLUGIN_URL . 'assets/js/admin.js',
            array('jquery'),
            CRAWLGUARD_VERSION,
            true
        );
        
        wp_localize_script('crawlguard-admin', 'crawlguard_ajax', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('crawlguard_nonce')
        ));
    }
    
    public function ajax_get_analytics() {
        check_ajax_referer('crawlguard_nonce', 'nonce');

        if (class_exists('CrawlGuard_API_Client')) {
            $api_client = new CrawlGuard_API_Client();
            $analytics = $api_client->get_analytics();
            wp_send_json_success($analytics);
        } else {
            wp_send_json_error('API Client not available');
        }
    }

    /**
     * AJAX handler for testing JWT tokens
     */
    public function ajax_test_jwt() {
        check_ajax_referer('crawlguard_nonce', 'nonce');

        $jwt_token = sanitize_text_field($_POST['jwt_token'] ?? '');

        if (empty($jwt_token)) {
            wp_send_json_error('No JWT token provided');
            return;
        }

        $validation_result = $this->jwt_validator->validate_token($jwt_token);

        if ($validation_result['valid']) {
            $user_info = $this->jwt_validator->extract_user_info($validation_result['payload']);
            wp_send_json_success(array(
                'message' => 'JWT token is valid',
                'user_info' => $user_info,
                'payload' => $validation_result['payload']
            ));
        } else {
            wp_send_json_error('JWT validation failed: ' . $validation_result['error']);
        }
    }

    /**
     * AJAX handler for generating sample JWT tokens
     */
    public function ajax_generate_sample_jwt() {
        check_ajax_referer('crawlguard_nonce', 'nonce');

        $custom_data = array();

        // Allow customization of sample data
        if (!empty($_POST['email'])) {
            $custom_data['email'] = sanitize_email($_POST['email']);
        }
        if (!empty($_POST['plan'])) {
            $custom_data['subscription_plan'] = sanitize_text_field($_POST['plan']);
        }

        $sample_token = $this->jwt_validator->generate_sample_token($custom_data);

        wp_send_json_success(array(
            'token' => $sample_token,
            'message' => 'Sample JWT token generated successfully'
        ));
    }

    public function admin_notices() {
        // Only show on CrawlGuard pages
        $screen = get_current_screen();
        if (!$screen || strpos($screen->id, 'crawlguard') === false) {
            return;
        }

        $options = get_option('crawlguard_options', array());
        $api_key = $options['api_key'] ?? '';
        $jwt_error = get_transient('crawlguard_jwt_error');

        // Show JWT validation error
        if ($jwt_error) {
            ?>
            <div class="notice notice-error is-dismissible">
                <p><strong>CrawlGuard JWT Error:</strong> <?php echo esc_html($jwt_error); ?></p>
                <p>Please check your JWT token in the settings and ensure it's valid.</p>
            </div>
            <?php
            return;
        }

        // Show setup notice if no API key
        if (empty($api_key)) {
            ?>
            <div class="notice notice-info is-dismissible">
                <p><strong>CrawlGuard Setup:</strong> Welcome! Please configure your JWT token or API key in the settings below to start protecting your site from AI bots.</p>
            </div>
            <?php
        } elseif ($this->user_data && !isset($this->user_data['legacy_key'])) {
            // Show success notice for valid JWT
            $subscription_status = $this->user_data['subscription_status'] ?? 'unknown';
            if ($subscription_status !== 'active') {
                ?>
                <div class="notice notice-warning is-dismissible">
                    <p><strong>CrawlGuard Subscription:</strong> Your subscription status is "<?php echo esc_html($subscription_status); ?>". Some features may be limited.</p>
                </div>
                <?php
            }
        }
    }
}
